#!/bin/bash

# <PERSON>ript to fix Nginx template http2 syntax on remote servers
# This fixes the "unknown directive http2" error

echo "🔧 Fixing Nginx template http2 syntax on remote servers..."

# Define servers
SERVERS=("abuja" "lagos" "kano")
SSH_HOST="dovely.tech"
SSH_USER="root"
SSH_KEY_PATH="/home/<USER>/.ssh"

# Template path on remote server (adjust as needed)
TEMPLATE_PATH="/root/scripts/wp/template"

for server in "${SERVERS[@]}"; do
    echo "📡 Connecting to ${server}.${SSH_HOST}..."
    
    # Create backup of original template
    ssh -i "${SSH_KEY_PATH}/${server}" "${SSH_USER}@${server}.${SSH_HOST}" \
        "cp ${TEMPLATE_PATH} ${TEMPLATE_PATH}.backup.$(date +%Y%m%d_%H%M%S)" 2>/dev/null
    
    # Fix http2 syntax - replace "http2 on;" with "http2" in listen directives
    ssh -i "${SSH_KEY_PATH}/${server}" "${SSH_USER}@${server}.${SSH_HOST}" \
        "sed -i 's/listen 443 ssl;/listen 443 ssl http2;/g; s/listen \[::\]:443 ssl;/listen [::]:443 ssl http2;/g; /http2 on;/d' ${TEMPLATE_PATH}" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo "✅ Fixed Nginx template on ${server}"
    else
        echo "❌ Failed to fix template on ${server}"
    fi
done

echo "🎉 Nginx template fix completed!"
echo ""
echo "📋 Changes made:"
echo "  - Changed 'listen 443 ssl;' to 'listen 443 ssl http2;'"
echo "  - Changed 'listen [::]:443 ssl;' to 'listen [::]:443 ssl http2;'"
echo "  - Removed standalone 'http2 on;' directives"
echo ""
echo "🧪 Test with: sudo nginx -t"
