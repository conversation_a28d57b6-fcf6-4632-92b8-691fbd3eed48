import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { IWordPress } from 'src/common/interfaces/wp.interface';
import { Server } from 'src/domain/sites/enums/servers.enum';
import { SshService } from 'src/ssh/ssh.service';

@Processor('ssh')
export class QueueProcessor {
  constructor(private readonly sshService: SshService) {}

  @Process('provision-wp')
  async handleProvision(job: Job<IWordPress>) {
    console.log(
      `🚀 [SSH Job Started] ID: ${job.id}, Domain: ${job.data.domain}`,
    );
    try {
      await this.sshService.provisionWP(job.data);
      console.log(
        `✅ [SSH Job Success] ID: ${job.id}, Domain: ${job.data.domain}`,
      );
    } catch (error) {
      console.error(`❌ [SSH Job Failed] ID: ${job.id}`, error);
      throw error;
    }
  }

  @Process('check-health')
  async handleHealthCheck(job: Job<{ server: Server }>) {
    try {
      const result = await this.sshService.checkHealth(job.data.server);
      console.log(`🔍 Health check for ${job.data.server}: ${result}`);
      return result;
    } catch (error) {
      console.error(`❌ Health check failed for ${job.data.server}`, error);
      throw error;
    }
  }
}
