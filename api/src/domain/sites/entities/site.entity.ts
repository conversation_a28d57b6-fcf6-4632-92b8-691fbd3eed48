import { RegistryDates } from 'src/common/embedded/registry-dates.embedded';
import { User } from 'src/domain/users/entities/user.entity';
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Server } from '../enums/servers.enum';

export enum SiteType {
  WORDPRESS = 'wordpress',
  CONTAINER = 'container',
}

export enum SiteStatus {
  ACTIVE = 'active',
  DISABLED = 'disabled',
}

@Entity('sites')
export class Site {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  domain: string;

  @Column({
    type: 'varchar',
    length: 255,
    default: 'Ultra-fast hosting from Onclouds',
  })
  title: string;

  @Column({
    type: 'varchar',
    length: 255,
    default: 'Built for speed. Powered by simplicity.',
  })
  tagline: string;

  @Column({
    type: 'enum',
    enum: SiteType,
    enumName: 'site_type',
    default: SiteType.WORDPRESS,
  })
  type: SiteType;

  @Column({
    type: 'enum',
    enum: SiteStatus,
    enumName: 'site_status',
    default: SiteStatus.ACTIVE,
  })
  status: SiteStatus;

  @Column({ type: 'varchar', length: 50 })
  tag: string;

  @Column({ type: 'varchar', length: 255 })
  adminUser: string;

  @Column({ type: 'varchar', length: 255 })
  adminEmail: string;

  @Column({ type: 'varchar', length: 255 })
  adminPassword: string;

  @Column({ type: 'varchar', length: 255 })
  dbName: string;

  @Column({ type: 'varchar', length: 255 })
  dbUser: string;

  @Column({ type: 'varchar', length: 255 })
  dbPassword: string;

  @Column({ type: 'varchar', length: 255, default: 'localhost' })
  dbHost: string;

  @Column({
    type: 'enum',
    enum: Server,
    enumName: 'server',
  })
  server: Server;

  @ManyToOne(() => User, (user) => user.sites)
  owner: User;

  @Column(() => RegistryDates, { prefix: false })
  registryDates: RegistryDates;
}
