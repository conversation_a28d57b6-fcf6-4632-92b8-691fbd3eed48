import {
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { pathExistsSync, readFileSync } from 'fs-extra';
import { join } from 'path';
import { IWordPress } from 'src/common/interfaces/wp.interface';
import { Server } from 'src/domain/sites/enums/servers.enum';
import { AppConfig } from 'src/env/env.schema';
import { Client, ConnectConfig } from 'ssh2';

@Injectable()
export class SshService {
  constructor(@Inject(AppConfig) private readonly config: AppConfig) {}

  private getSshConfig(server: Server): ConnectConfig {
    const host = `${server.toLowerCase()}.${this.config.SSH_HOST}`;
    const keyPath = join(this.config.SSH_KEY_PATH, server.toLowerCase());

    if (!pathExistsSync(keyPath)) {
      throw new InternalServerErrorException(`SSH key not found: ${keyPath}`);
    }

    return {
      host,
      port: this.config.SSH_PORT,
      username: this.config.SSH_USER,
      privateKey: readFileSync(keyPath),
    };
  }

  private async connect(server: Server): Promise<Client> {
    const client = new Client();
    const sshConfig = this.getSshConfig(server);

    return new Promise((resolve, reject) => {
      client
        .on('ready', () => {
          resolve(client); // not store in `this.client` to avoid concurrency issues
        })
        .on('error', (err) => {
          reject(
            new InternalServerErrorException(
              `SSH connection failed → ${err.message}`,
            ),
          );
        })
        .connect(sshConfig);
    });
  }

  private async execCommands(
    commands: string[],
    server: Server,
  ): Promise<string[]> {
    const client = await this.connect(server);
    const results: string[] = [];

    try {
      for (const command of commands) {
        const result = await new Promise<string>((resolve, reject) => {
          // Use PTY for sudo commands to handle password prompts properly
          const needsPty = command.includes('sudo -S');
          const execOptions = needsPty ? { pty: true } : {};

          client.exec(command, execOptions, (err, stream) => {
            if (err) {
              return reject(err);
            }

            let stdout = '';
            let stderr = '';

            stream
              .on('close', (code: number) => {
                if (code !== 0 && stderr) {
                  return reject(
                    new InternalServerErrorException(
                      stderr || `Command failed with exit code ${code}`,
                    ),
                  );
                }
                resolve(stdout);
              })
              .on('data', (data: Buffer) => (stdout += data.toString()))
              .stderr.on('data', (data: Buffer) => (stderr += data.toString()));
          });
        });
        results.push(result);
      }
      return results;
    } finally {
      client.end();
    }
  }

  async checkHealth(server: Server): Promise<string> {
    try {
      const output = await this.execCommands(['uptime'], server);
      return output[0].trim();
    } catch (err) {
      throw new InternalServerErrorException(
        `Health check failed → ${
          err instanceof Error ? err.message : JSON.stringify(err)
        }`,
      );
    }
  }

  private formatSecrets(obj: Record<string, string>): string {
    return Object.entries(obj)
      .map(([key, value]) => `${key}="${this.escapeShellValue(value)}"`)
      .join('\n');
  }

  private escapeShellValue(value: string): string {
    // @TODO - refactor later, C.
    // Escape special characters for shell environment files in order of precedence
    return value
      .replace(/\\/g, '\\\\') // Escape backslashes first
      .replace(/"/g, '\\"') // Escape double quotes
      .replace(/\$/g, '\\$') // Escape dollar signs
      .replace(/`/g, '\\`'); // Escape backticks
  }

  async provisionWP(input: IWordPress) {
    const {
      domain,
      title,
      adminUser,
      adminEmail,
      adminPassword,
      dbName,
      dbUser,
      dbPassword,
      server,
    } = input;

    const secretsObj = {
      DOMAIN: domain,
      TITLE: title,
      ADMIN_USER: adminUser,
      ADMIN_EMAIL: adminEmail,
      ADMIN_PASSWORD: adminPassword,
      DB_NAME: dbName,
      DB_USER: dbUser,
      DB_PASSWORD: dbPassword,
    };

    const secrets = this.formatSecrets(secretsObj);

    console.log({ secrets });

    const secretPath = `${this.config.SSH_SECRETS_DIR}/${domain}.env`;
    const modulesPath = `${this.config.SSH_SCRIPTS_DIR}/wp`;

    try {
      const commands = [
        `cat > ${secretPath} << 'EOF'\n${secrets}\nEOF`,
        `chmod 644 ${secretPath}`,
        `${modulesPath}/setup-directories ${secretPath}`,
        `${modulesPath}/setup-database ${secretPath}`,
        `echo ${this.config.SSH_SUDO_PASSWORD} | sudo -S ${modulesPath}/setup-ssl-nginx ${secretPath}`,
        ` ${modulesPath}/deploy-wordpress ${secretPath}`,
      ];
      await this.execCommands(commands, server);

      return {
        results: [...commands],
        provisionOutput: commands[commands.length - 1],
      };
    } catch (err) {
      throw new InternalServerErrorException(
        `Provision failed → ${
          err instanceof Error ? err.message : JSON.stringify(err)
        }`,
      );
    }
  }
}
